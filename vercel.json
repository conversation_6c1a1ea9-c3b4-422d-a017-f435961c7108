{"installCommand": "npm install --legacy-peer-deps", "buildCommand": "sh build.sh", "outputDirectory": "dist", "rewrites": [{"source": "/_next/:path*", "destination": "https://cnc-odin.vercel.app/_next/:path*"}, {"source": "/images/:path*", "destination": "https://cnc-odin.vercel.app/images/:path*"}, {"source": "/static/:path*", "destination": "https://cnc-odin.vercel.app/static/:path*"}, {"source": "/favicon.ico", "destination": "https://cnc-odin.vercel.app/favicon.ico"}, {"source": "/robots.txt", "destination": "https://cnc-odin.vercel.app/robots.txt"}, {"source": "/sitemap.xml", "destination": "https://cnc-odin.vercel.app/sitemap.xml"}, {"source": "/property/:id/:subpath/:path*", "destination": "https://cnc-odin.vercel.app/property/:id/:subpath/:path*"}, {"source": "/property/:id/:subpath/", "destination": "https://cnc-odin.vercel.app/property/:id/:subpath/"}, {"source": "/property/:id/:subpath", "destination": "https://cnc-odin.vercel.app/property/:id/:subpath"}, {"source": "/property/:id/", "destination": "https://cnc-odin.vercel.app/property/:id/"}, {"source": "/property/:id", "destination": "https://cnc-odin.vercel.app/property/:id"}, {"source": "/lease/:id/:subpath/:path*", "destination": "https://cnc-odin.vercel.app/lease/:id/:subpath/:path*"}, {"source": "/lease/:id/:subpath/", "destination": "https://cnc-odin.vercel.app/lease/:id/:subpath/"}, {"source": "/lease/:id/:subpath", "destination": "https://cnc-odin.vercel.app/lease/:id/:subpath"}, {"source": "/lease/:id/", "destination": "https://cnc-odin.vercel.app/lease/:id/"}, {"source": "/lease/:id", "destination": "https://cnc-odin.vercel.app/lease/:id"}, {"source": "/lease/", "destination": "https://cnc-odin.vercel.app/lease/"}, {"source": "/lease", "destination": "https://cnc-odin.vercel.app/lease"}, {"source": "/(.*)", "destination": "/"}]}