{"installCommand": "npm install --legacy-peer-deps", "buildCommand": "sh build.sh", "outputDirectory": "dist", "redirects": [{"source": "/property/:id/:subpath/:path*", "destination": "https://cnc-odin.vercel.app/property/:id/:subpath/:path*", "permanent": false}, {"source": "/property/:id/:subpath/", "destination": "https://cnc-odin.vercel.app/property/:id/:subpath/", "permanent": false}, {"source": "/property/:id/:subpath", "destination": "https://cnc-odin.vercel.app/property/:id/:subpath", "permanent": false}, {"source": "/property/:id/", "destination": "https://cnc-odin.vercel.app/property/:id/", "permanent": false}, {"source": "/property/:id", "destination": "https://cnc-odin.vercel.app/property/:id", "permanent": false}, {"source": "/lease/:id/:subpath/:path*", "destination": "https://cnc-odin.vercel.app/lease/:id/:subpath/:path*", "permanent": false}, {"source": "/lease/:id/:subpath/", "destination": "https://cnc-odin.vercel.app/lease/:id/:subpath/", "permanent": false}, {"source": "/lease/:id/:subpath", "destination": "https://cnc-odin.vercel.app/lease/:id/:subpath", "permanent": false}, {"source": "/lease/:id/", "destination": "https://cnc-odin.vercel.app/lease/:id/", "permanent": false}, {"source": "/lease/:id", "destination": "https://cnc-odin.vercel.app/lease/:id", "permanent": false}, {"source": "/lease/", "destination": "https://cnc-odin.vercel.app/lease/", "permanent": false}, {"source": "/lease", "destination": "https://cnc-odin.vercel.app/lease", "permanent": false}]}